<template>
  <view class="under-care-container">
    <scroll-view
      class="under-care-list"
      scroll-y="true"
      :scroll-with-animation="true"
      @scrolltolower="scrollToLower"
      :refresher-enabled="true"
      :refresher-triggered="refresherState"
      @refresherrefresh="bindrefresherrefresh"
    >
      <view class="list-content">
        <!-- 调试信息 -->
        <view style="background: yellow; padding: 20rpx; margin: 20rpx;">
          <text>调试信息：dataList长度 = {{ dataList.length }}</text>
          <text v-if="dataList.length > 0">第一项数据：{{ JSON.stringify(dataList[0]) }}</text>
        </view>

        <view v-for="(item, index) in dataList" :key="index" class="list-item">
          <!-- 简化版本用于调试 -->
          <view style="padding: 20rpx; background: lightblue;">
            <text>项目 {{ index + 1 }}: {{ item.earTagNo || '无耳标号' }}</text>
            <text>时间: {{ item.operateTime || '无时间' }}</text>
          </view>

          <!-- 原始完整版本 -->
          <view class="item-header">
            <text class="item-time">{{ item.operateTime }}</text>
          </view>
          <view class="item-content">
            <view class="content-row">
              <text class="content-label">耳标号：</text>
              <text class="content-value">{{ item.earTagNo }}</text>
            </view>
            <view class="content-row">
              <text class="content-label">活畜状态：</text>
              <text class="content-value">
                <text class="status-tag" :class="getSpiritStateClass(item.livestockSpiritState)">
                  {{ getSpiritStateText(item.livestockSpiritState) }}
                </text>
              </text>
            </view>

            <view class="content-row">
              <text class="content-label">采食情况：</text>
              <text class="content-value">
                <text class="status-tag" :class="getEatStateClass(item.livestockEatState)">
                  {{ getEatStateText(item.livestockEatState) }}
                </text>
              </text>
            </view>
            <view class="content-row" >
              <text class="content-label">异常描述：</text>
              <text class="content-value">{{ item.livestockErrorDesc || '-' }}</text>
            </view>
            <view class="content-row" v-if="item.remark">
              <text class="content-label">备注：</text>
              <text class="content-value remark-text">{{ item.remark }}</text>
            </view>
          </view>
        </view>

        <nullList v-if="isEmpty" />
        <view v-if="!noMore && dataList.length > 0" class="load-more">加载更多...</view>
        <view v-if="noMore && dataList.length > 0" class="load-more">没有更多数据了</view>
      </view>
    </scroll-view>

    <view class="fixed-add-btn" @click="addRecord">
      <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/add.png" alt="" />
    </view>
  </view>
</template>

<script>
import { dailyPage } from '@/api/pages/livestock/underCare'
import nullList from '@/components/null-list/index.vue'

export default {
  name: 'DailyRecord',
  components: {
    nullList
  },
  data() {
    return {
      refresherState: false,
      noMore: false,
      isEmpty: false,
      list: [],
      dataList: [],
      pageNum: 1,
      pageSize: 10,
      // 字典数据
      spiritStateDict: {
        1: '正常',
        2: '食欲不振',
        3: '异常活跃',
        4: '患病症状'
      },
      eatStateDict: {
        1: '正常采食',
        2: '采食减少',
        3: '采食增加',
        4: '完全不采食'
      }
    }
  },
  created() {
    console.log('组件创建时 list:', this.list)
  },
  mounted() {
    console.log('组件mounted，开始调用getList')
    console.log('mounted时dataList:', this.dataList)
    this.getList()
  },
  methods: {
    getList() {
      console.log('getList方法被调用')
      console.log('调用前dataList:', this.dataList)
      console.log('调用前dataList类型:', typeof this.dataList)

      uni.showLoading({
        title: '加载中',
        icon: 'none'
      })
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        earTagNo: '',
        startTime: '',
        endTime: ''
      }

      console.log('准备调用API，参数:', params)
      console.log('API调用前dataList:', this.dataList)

      dailyPage(params).then(response => {
        console.log('API调用成功，response:', response)
        if (response.code === 200) {
          const newList = response.result?.list || []
          // 确保 total 是数字类型
          const total = parseInt(response.result?.total || 0)

          console.log('新数据列表:', newList)
          console.log('新数据列表类型:', typeof newList)
          console.log('新数据列表是否为数组:', Array.isArray(newList))
          console.log('新数据列表长度:', newList.length)
          console.log('总数:', total)
          console.log('当前页码:', this.pageNum)

          if (this.pageNum >= 2) {
            this.list = [...this.list, ...newList]
            this.dataList = [...this.list]  // 立即更新 dataList
            this.noMore = this.list.length >= total
          } else {
            if (total >= 1 && newList.length > 0) {
              this.isEmpty = false
              this.list = [...newList]  // 使用展开运算符确保响应式
              this.dataList = [...this.list]  // 立即更新 dataList
              this.noMore = this.list.length >= total
            } else {
              this.isEmpty = true
              this.list = []
              this.dataList = []  // 清空 dataList
            }
          }
          // 强制触发响应式更新
          this.$forceUpdate()
        } else {
          console.error('API 返回错误:', response.code, response.message)
          this.isEmpty = true
          this.list = []
          this.dataList = []  // 清空 dataList
        }
        console.log('dataList:', this.dataList.length)
        console.log('dataList内容:', this.dataList)
        console.log('最终列表数据:', this.list)
        console.log('isEmpty状态:', this.isEmpty)
        console.log('Vue实例dataList:', JSON.stringify(this.dataList))
      }).catch(error => {
        console.error('获取日常记录列表失败:', error)
        this.isEmpty = true
        this.list = []
        this.dataList = []
      }).finally(() => {
        uni.hideLoading()
      })
    },

    scrollToLower() {
      if (this.noMore) return
      this.pageNum++
      this.getList()
    },

    bindrefresherrefresh() {
      this.refresherState = true
      this.pageNum = 1
      this.noMore = false
      this.getList()
      setTimeout(() => {
        this.refresherState = false
        this.$toast('刷新成功')
      }, 1000)
    },

    getSpiritStateText(value) {
      return this.spiritStateDict[value] || '未知'
    },

    getEatStateText(value) {
      return this.eatStateDict[value] || '未知'
    },

    getSpiritStateClass(value) {
      if (value === 1) return 'normal'
      if (value === 4) return 'abnormal'
      return 'warning'
    },

    getEatStateClass(value) {
      if (value === 1 || value === 3) return 'normal'
      if (value === 4) return 'abnormal'
      return 'warning'
    },

    addRecord() {
      // 新增记录逻辑
      this.$toast('新增日常记录')
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/underCareList.scss';
</style>
